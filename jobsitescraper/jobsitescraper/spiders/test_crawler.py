import json
import uuid
from jobsitescraper.log_manager import CustomLogger
from jobsitescraper.utils import env
from scrapy.exceptions import CloseSpider
from bs4 import BeautifulSoup
from datetime import datetime
from datetime import timezone
import sys, traceback
import scrapy
import re


class stoecklimedical_ch_CrawlingManager(scrapy.Spider):
    name = "stoecklimedical.ch"
    close_down = False
    config = {}
    isLive = env("PRODUCTION")
    count = 0

    def __init__(self, _config=None, *args, **kwargs):
        super().__init__(**kwargs)
        if self.isLive == "True":
            self.config = _config
        else:
            self.config = self.get_config()

    def get_config(self):
        config = {}
        config["SourceKey"] = "stoecklimedical.ch"
        config["BaseUrl"] = "https://www.stoecklimedical.ch"
        config["StartUrl"] = "https://www.stoecklimedical.ch/offene-stellen/"
        config["SourceCountry"] = "ch"
        config["CompanyName"] = "Stöckli Medical AG"
        config["LangCode"] = "de"
        config["Upload"] = True
        config["IsActive"] = True
        config["Custom"] = True
        config['DeleteAllJobsOnStart'] = True
        return config


    def start_requests(self):
        try:
            if self.config is None:
                CustomLogger.LogEvent(self.name, "No Config Read")
            else:
                CustomLogger.LogEvent(self.config["SourceKey"], " Crawler Started")
                yield scrapy.Request(self.config["StartUrl"], method='GET', callback=self.parse_all)
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def create_uuid_for_job(self):
        unique_id = uuid.uuid4()
        return str(unique_id)


    def parse_all(self, response):
        try:
            for i in response.xpath('//ul[@class="accordion"]/li'):
                title = i.xpath('.//a/text()').get()
                loc = "Switzerland"
                website_text = i.xpath('.//div').get()
                # website_text = response.body.decode("utf-8")
                description = BeautifulSoup(website_text.replace("<", " <"), "html.parser")
                # description = jobs_soup.find('div', {"id": "pageData"})
                if description is not None:
                    for tag in description.find_all(['a', 'svg', 'script', 'img']):
                        tag.decompose()
                    cleanContent = re.sub('\s+', ' ', description.get_text())
                    rawContent = re.sub('\s+', ' ', description.decode_contents())
                else:
                    cleanContent = ''
                    rawContent = ''
                ad = {}
                ad['JobTitle'] = title
                ad['JobLocation'] = loc
                ad["SourceURL"] = response.url
                ad['CompanyName'] = self.config["CompanyName"]
                ad['SourceCountry'] = self.config["SourceCountry"]
                ad['SourceKey'] = self.config["SourceKey"]
                ad['SourceLangCode'] = self.config["LangCode"]
                ad['CompanyLogoFileURL'] = 'https://exhibitors.ifat.de/nfmedb/messe_1851/img/stammdaten/04409599.jpg'
                ad['SourceUID'] = self.create_uuid_for_job()
                ad['CrawlTimestamp'] = datetime.now(timezone.utc).astimezone().isoformat()
                ad['PostedDate'] = datetime.now(timezone.utc).astimezone().isoformat()
                ad['CleanContent'] = cleanContent
                ad['RawContent'] = rawContent
                email_list = re.findall('\S+@\S+', ad['CleanContent'])
                phone_list = re.findall(r'[\+\(]?[1-9][0-9 \-\(\)]{8,}[0-9]', ad['CleanContent'])
                if len(email_list) > 0:
                    ad['JobContactEmails'] = email_list[0]
                if len(phone_list) > 0:
                    ad['JobContactPhone'] = phone_list[0]
                if self.config["Upload"] is True:
                    self.count += 1
                    yield ad
                else:
                    CustomLogger.LogEvent(self.config["SourceKey"], "Scraped But not uploaded")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
            print(traceback.format_exc())
            sys.stdout.flush()

    def close(self, reason):
        try:
            CustomLogger.LogEvent(self.config["SourceKey"], f"Crawler Stopped, Total Jobs: {self.count}")
        except Exception as e:
            CustomLogger.LogEvent(self.config["SourceKey"], str(e))
